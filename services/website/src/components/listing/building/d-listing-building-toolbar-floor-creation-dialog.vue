<template>
    <v-dialog v-model="isVisible"
              max-width="600">
        <d-card is-in-dialog
                title="Stockwerk erstellen">
            <d-divider/>
            <v-container class="ma-0 py-1 px-4"
                         fluid>
                <v-row class="ma-0 pa-0"
                       dense
                       justify="space-evenly">
                    <v-col align-self="center"
                           cols="12"
                           md="5">
                        <v-container class="px-0 mx-0"
                                     fluid>
                            <v-row dense>
                                <v-col>
                                    <d-text-field v-model="width"
                                                  label="Breite"
                                                  type="number">
                                        <template #appendInner>
                                            <d-input-unit unit="CENTIMETER"/>
                                        </template>
                                    </d-text-field>
                                </v-col>
                            </v-row>
                            <v-row dense>
                                <v-col>
                                    <d-text-field v-model="depth"
                                                  label="Tiefe"
                                                  type="number">
                                        <template #appendInner>
                                            <d-input-unit unit="CENTIMETER"/>
                                        </template>
                                    </d-text-field>
                                </v-col>
                            </v-row>
                            <v-row justify="center">
                                <v-col cols="auto">
                                    <d-btn :disabled="width === null || depth === null"
                                           size="large"
                                           text="Neu anlegen"
                                           type="secondary"
                                           @click="createFloor"/>
                                </v-col>
                            </v-row>
                        </v-container>
                    </v-col>

                    <template v-if="floors.length > 0">
                        <v-col v-if="mdAndUp"
                               cols="auto">
                            <d-divider class="text-center"
                                       style="width: 2%; height: 100%;"
                                       vertical/>
                        </v-col>
                        <v-col v-else
                               cols="12">
                            <v-divider/>
                        </v-col>

                        <v-col align-self="center"
                               cols="12"
                               md="5">
                            <v-container class="px-0 mx-0"
                                         fluid>
                                <v-row dense>
                                    <v-col>
                                        <d-select v-model="selectedFloor"
                                                  :display-name-supplier="f => t(`listing.building.cad.floorLevelInfo.type.long.${f.levelInfo.levelType}`, {
                                                n: f.levelInfo.number
                                              })"
                                                  :id-supplier="f => f.id"
                                                  :items="floors"
                                                  label="Stockwerk"/>
                                    </v-col>
                                </v-row>
                                <v-row justify="center">
                                    <v-col cols="auto">
                                        <d-btn :disabled="selectedFloor === null"
                                               size="large"
                                               text="Kopieren"
                                               type="secondary"
                                               @click="copyFloor(selectedFloor!)"/>
                                    </v-col>
                                </v-row>
                            </v-container>
                        </v-col>
                    </template>
                </v-row>
            </v-container>
        </d-card>
    </v-dialog>
</template>

<script lang="ts"
        setup>
    import {useI18n} from "vue-i18n";
    import DCard from "@/adapter/vuetify/theme/components/card/d-card.vue";
    import {computed, inject, shallowRef} from "vue";
    import {BUILDING_FLOOR_SLAB_THICKNESS_DEFAULT, changeFloorSlabCeilingThickness, changeFloorSlabFloorThickness, createEmptyShapeRepresentation, createNewWallCustomData, DBuildingRendererInjection, deepCopyShapeRepresentation, transformationOfShapeRepresentation} from "@/components/listing/building/building";
    import DDivider from "@/adapter/vuetify/theme/components/divider/d-divider.vue";
    import DBtn from "@/adapter/vuetify/theme/components/button/d-btn.vue";
    import DSelect from "@/adapter/vuetify/theme/components/input/d-select.vue";
    import {ConstructionPart, Floor, FloorLevelInfo, Shape, ShapeRepresentation, Vector3D, Wall} from "@/adapter/graphql/generated/graphql";
    import {Optional} from "@/model/Optional";
    import {useDisplay} from "vuetify";
    import DTextField from "@/adapter/vuetify/theme/components/input/d-text-field.vue";
    import DInputUnit from "@/adapter/vuetify/theme/components/input/d-input-unit.vue";
    import {Euler, MathUtils, Matrix4} from "three";
    import {EnsureDefined} from "@/adapter/graphql/mapper/graphql-mapper";
    import {createIdentityMatrix, matrix4ToTransformationMatrixArray} from "@/components/listing/building-creation/pipeline/building-creation-pipeline";
    import {changeShapeType} from "@/components/listing/building/shape-representation-utils";
    import {tDecomposeMatrix, tDeskewAngle2D} from "@/adapter/three/three-utility";
    import {wallOrOpeningToLineSegments} from "@/components/listing/building/wall-and-opening-lines";
    import {BuildingPipelineBuilder} from "@/components/listing/building/pipeline/BuildingPipelineBuilder";
    import {v4 as uuidv4} from "uuid";

    const props = defineProps<{
        level: number
    }>()

    const isVisible = defineModel<boolean>("modelValue", {
        required: true
    })

    const renderer = inject(DBuildingRendererInjection)!

    const floors = computed<readonly Floor[]>(() => renderer.building.value.floors)
    const selectedFloor = shallowRef<Optional<Floor>>(null)
    const width = shallowRef<Optional<number>>(null)
    const depth = shallowRef<Optional<number>>(null)

    const {mdAndUp} = useDisplay()
    const {t} = useI18n()

    async function copyFloor(floor: Floor) {
        isVisible.value = false

        const building = renderer.building.value

        // Deep copy the floor slab with new ID
        function copyConstructionPart(original: ConstructionPart): EnsureDefined<ConstructionPart> {
            return {
                __typename: 'ConstructionPart',
                customData: original.customData ? [...original.customData] : [],
                displayId: null,
                foreignRoomId: original.foreignRoomId,
                id: uuidv4(), // Generate new ID
                shapeRepresentation: deepCopyShapeRepresentation(original.shapeRepresentation),
                type: original.type,
            } satisfies EnsureDefined<ConstructionPart>
        }

        // Deep copy a wall with new ID and copy all its openings
        function copyWall(original: Wall): EnsureDefined<Wall> {
            return {
                __typename: 'Wall',
                customData: original.customData ? [...original.customData] : [],
                displayId: null,
                id: uuidv4(), // Generate new ID
                isExterior: original.isExterior,
                isIntermediate: original.isIntermediate,
                isPartition: original.isPartition,
                openings: original.openings.map(copyConstructionPart), // Copy all openings with new IDs
                roomIds: original.roomIds ? [...original.roomIds] : null,
                shapeRepresentation: deepCopyShapeRepresentation(original.shapeRepresentation),
                sizeAdjustmentUserX: original.sizeAdjustmentUserX,
                sizeAdjustmentUserXRelatedWallIds: original.sizeAdjustmentUserXRelatedWallIds ? [...original.sizeAdjustmentUserXRelatedWallIds] : [],
                sizeAdjustmentUserY: original.sizeAdjustmentUserY,
                sizeAdjustmentX: original.sizeAdjustmentX,
                sizeAdjustmentY: original.sizeAdjustmentY,
                sizeAdjustmentZ: original.sizeAdjustmentZ,
            } satisfies EnsureDefined<Wall>
        }

        // Create the copied floor with new ID
        const copiedFloor: EnsureDefined<Floor> = {
            __typename: 'Floor',
            ceilingSlab: floor.ceilingSlab ? copyConstructionPart(floor.ceilingSlab) : null,
            customData: floor.customData ? [...floor.customData] : [],
            displayId: null,
            floorSlab: copyConstructionPart(floor.floorSlab), // Copy floor slab with new ID
            furniture: null, // Ignore furniture as requested
            id: uuidv4(), // Generate new ID for floor
            level: props.level, // Set to the target level
            levelInfo: {
                __typename: "FloorLevelInfo",
                levelType: "...",
                number: 0
            } satisfies EnsureDefined<FloorLevelInfo>,
            pointsOfInterest: null, // Ignore as requested
            roofAreas: null, // Ignore as requested
            rooms: [], // Ignore rooms as requested
            shapeRepresentation: deepCopyShapeRepresentation(floor.shapeRepresentation),
            unrecognizedRooms: null, // Ignore as requested
            walls: floor.walls.map(copyWall), // Copy all walls with their openings
        }

        // Insert the copied floor at the correct level (same logic as createFloor)
        renderer.snappingManager.isSpecialModeEnabled = true

        for (let i = 0; i < building.floors.length; ++i) {
            const selectedFloor = building.floors[i];
            if (selectedFloor.level >= props.level) {
                renderer.snappingManager.removeFloor(selectedFloor)
                ++selectedFloor.level
                renderer.snappingManager.addFloor(selectedFloor)
            }
        }

        // Insert at index
        building.floors.splice(building.floors.findIndex(f => f.level >= props.level), 0, copiedFloor)
        renderer.snappingManager.addFloor(copiedFloor)

        renderer.snappingManager.floorsChangedCounter.value++
        renderer.snappingManager.isSpecialModeEnabled = false

        building.floors.sort((a, b) => a.level - b.level)

        // Adjust slab heights (same logic as createFloor)
        const floorIndex = building.floors.findIndex(f => f.id === copiedFloor.id)
        const bottomFloor = floorIndex <= 0 ? null : building.floors[floorIndex - 1]
        const topFloor = floorIndex >= building.floors.length - 1 ? null : building.floors[floorIndex + 1]

        if (bottomFloor !== null) {
            changeFloorSlabCeilingThickness(renderer, bottomFloor, BUILDING_FLOOR_SLAB_THICKNESS_DEFAULT, false)
        }
        if (topFloor !== null) {
            changeFloorSlabFloorThickness(renderer, topFloor, BUILDING_FLOOR_SLAB_THICKNESS_DEFAULT, false)
        }

        renderer.traversableBuilding.refresh()

        await BuildingPipelineBuilder
            .create("CopyFloor", renderer)
            .save(true)
            .build()
            .execute()
    }

    async function createFloor() {
        isVisible.value = false

        const building = renderer.building.value

        // Calculate dimensions once at the top
        const wdth = width.value! / 100 // Convert from cm to meters
        const dpth = depth.value! / 100 // Convert from cm to meters
        const halfWidth = wdth / 2
        const halfDepth = dpth / 2

        function createWall(rotationStep: 1 | 2 | 3 | 4): EnsureDefined<Wall> {
            // Determine wall dimensions and distance from center based on rotation step
            // Following the original pattern: all walls same thickness and height, but different lengths
            const isWidthWall = rotationStep === 1 || rotationStep === 3 // Front/back walls run along width
            const wallLength = isWidthWall ? wdth : dpth
            const distanceFromCenter = isWidthWall ? halfDepth : halfWidth

            const wallThickness = 0.1 // Keep wall thickness constant at 10cm
            const wallHeight = 2 // Standard wall height in meters (matching original)

            return {
                __typename: 'Wall',
                customData: createNewWallCustomData(),
                displayId: null,
                id: uuidv4(),
                isExterior: null,
                isIntermediate: null,
                isPartition: null,
                openings: [],
                roomIds: null,
                shapeRepresentation: {
                    __typename: 'ShapeRepresentation',
                    shape: {
                        __typename: 'Box',
                        depth: wallThickness,
                        height: wallHeight,
                        width: wallLength,
                    } satisfies EnsureDefined<Shape>,
                    transformationMatrix: matrix4ToTransformationMatrixArray(
                        new Matrix4().identity()
                            .multiply(new Matrix4().makeRotationY(Math.PI / 2 * rotationStep))
                            .multiply(new Matrix4().makeTranslation(0, 0, distanceFromCenter))
                            .multiply(new Matrix4().makeTranslation(0, 1, 0))
                    ),
                } satisfies EnsureDefined<ShapeRepresentation>,
                sizeAdjustmentUserX: 0,
                sizeAdjustmentUserXRelatedWallIds: [],
                sizeAdjustmentUserY: 0,
                sizeAdjustmentX: 0,
                sizeAdjustmentY: 0,
                sizeAdjustmentZ: 0,
            } satisfies EnsureDefined<Wall>;
        }

        const floor: EnsureDefined<Floor> = {
            __typename: 'Floor',
            ceilingSlab: null,
            customData: [],
            displayId: null,
            floorSlab: {
                __typename: 'ConstructionPart',
                customData: [],
                displayId: null,
                foreignRoomId: null,
                id: uuidv4(),
                shapeRepresentation: {
                    __typename: 'ShapeRepresentation',
                    shape: {
                        __typename: 'Polygon',
                        extrusion: BUILDING_FLOOR_SLAB_THICKNESS_DEFAULT,
                        extrusionDirection: {
                            __typename: 'Vector3D',
                            x: 0,
                            y: 1,
                            z: 0
                        } satisfies EnsureDefined<Vector3D>,
                        holes: null,
                        vertices: [
                            {
                                __typename: 'Vector3D',
                                x: -halfWidth,
                                y: -halfDepth,
                                z: 0
                            } satisfies EnsureDefined<Vector3D>,
                            {
                                __typename: 'Vector3D',
                                x: halfWidth,
                                y: -halfDepth,
                                z: 0
                            } satisfies EnsureDefined<Vector3D>,
                            {
                                __typename: 'Vector3D',
                                x: halfWidth,
                                y: halfDepth,
                                z: 0
                            } satisfies EnsureDefined<Vector3D>,
                            {
                                __typename: 'Vector3D',
                                x: -halfWidth,
                                y: halfDepth,
                                z: 0
                            } satisfies EnsureDefined<Vector3D>,
                        ]
                    } satisfies EnsureDefined<Shape>,
                    transformationMatrix: createIdentityMatrix(),
                } satisfies EnsureDefined<ShapeRepresentation>,
                type: 'FLOOR',
            } satisfies EnsureDefined<ConstructionPart>,
            furniture: null,
            id: uuidv4(),
            level: props.level,
            levelInfo: {
                __typename: "FloorLevelInfo",
                levelType: "...",
                number: 0
            } satisfies EnsureDefined<FloorLevelInfo>,
            pointsOfInterest: null,
            roofAreas: null,
            rooms: [],
            shapeRepresentation: changeShapeType(createEmptyShapeRepresentation(), "Polygon", false)!,
            unrecognizedRooms: null,
            walls: [
                createWall(1),
                createWall(2),
                createWall(3),
                createWall(4),
            ],
        }

        function onRotate(degrees: number) {
            const transformation = transformationOfShapeRepresentation(floor.shapeRepresentation)

            const [translation, rotation, scale] = tDecomposeMatrix(transformation)
            const euler = new Euler().setFromQuaternion(rotation, 'YXZ')
            euler.y = MathUtils.degToRad(degrees)
            rotation.setFromEuler(euler)

            const newTransformation = new Matrix4().compose(translation, rotation, scale)

            floor.shapeRepresentation.transformationMatrix = matrix4ToTransformationMatrixArray(newTransformation)
        }

        onRotate(0)

        const lineSegments = floor.walls.flatMap(wall => wallOrOpeningToLineSegments(renderer, wall, wall, true))
        const deskewAngleInRadians = tDeskewAngle2D(lineSegments) ?? 0

        if (deskewAngleInRadians !== 0) {
            const degrees = Math.round(MathUtils.radToDeg(deskewAngleInRadians) * 100) / 100 //round to 2 decimal places
            onRotate(degrees)
        }

        renderer.snappingManager.isSpecialModeEnabled = true

        for (let i = 0; i < building.floors.length; ++i) {
            const selectedFloor = building.floors[i];
            if (selectedFloor.level >= props.level) {
                renderer.snappingManager.removeFloor(selectedFloor)
                ++selectedFloor.level
                renderer.snappingManager.addFloor(selectedFloor)
            }
        }

        //insert at index
        building.floors.splice(building.floors.findIndex(f => f.level >= props.level), 0, floor)
        renderer.snappingManager.addFloor(floor)

        renderer.snappingManager.floorsChangedCounter.value++
        renderer.snappingManager.isSpecialModeEnabled = false

        building.floors.sort((a, b) => a.level - b.level)

        //Obere und untere Slabhöhen anpassen
        const floorIndex = building.floors.findIndex(f => f.id === floor.id)
        const bottomFloor = floorIndex <= 0 ? null : building.floors[floorIndex - 1]
        const topFloor = floorIndex >= building.floors.length - 1 ? null : building.floors[floorIndex + 1]

        if (bottomFloor !== null) {
            changeFloorSlabCeilingThickness(renderer, bottomFloor, BUILDING_FLOOR_SLAB_THICKNESS_DEFAULT, false)
        }
        if (topFloor !== null) {
            changeFloorSlabFloorThickness(renderer, topFloor, BUILDING_FLOOR_SLAB_THICKNESS_DEFAULT, false)
        }

        renderer.traversableBuilding.refresh()

        await BuildingPipelineBuilder
            .create("AddFloor", renderer)
            .save(true)
            .build()
            .execute()
    }
</script>

<style scoped>
</style>