import {Box, Building, BuildingPointOfInterest, ConstructionPart, Floor, FloorLevelInfo, FlowConfigField, FlowConfigFieldValue, Furniture, Polygon, Ring, RoofArea, Room, Shape, ShapeRepresentation, Vector2D, Vector3D, Wall} from "@/adapter/graphql/generated/graphql";
import {deepCopyMatrixArray} from "@/components/listing/building/building";
import {EnsureDefined} from "@/adapter/graphql/mapper/graphql-mapper";

export function mapToBuilding(building: Building): EnsureDefined<Building> {
    const floorLevels = new Set<number>();
    for (const floor of building.floors) {
        floorLevels.add(floor.level);
    }

    return {
        __typename: "Building",
        customData: building.customData.map(mapToFlowConfigFieldValue),
        floors: building.floors.map(mapToFloor),
        id: building.id,
        displayId: building.displayId === undefined || building.displayId === null ? null : building.displayId,
        floorLevels: Array.from(floorLevels).sort(),
        shapeRepresentation: mapToShapeRepresentation(building.shapeRepresentation),
        pointsOfInterest: building.pointsOfInterest?.map(mapToPointOfInterest) ?? [],
        rotationYCorrectionAngle: building.rotationYCorrectionAngle,
    }
}

function mapToPointOfInterest(pointOfInterest: BuildingPointOfInterest): EnsureDefined<BuildingPointOfInterest> {
    return {
        __typename: "BuildingPointOfInterest",
        customData: pointOfInterest.customData.map(mapToFlowConfigFieldValue),
        id: pointOfInterest.id,
        displayId: pointOfInterest.displayId === undefined || pointOfInterest.displayId === null ? null : pointOfInterest.displayId,
        transformationMatrix: deepCopyMatrixArray(pointOfInterest.transformationMatrix),
    }
}

function mapToFloor(floor: Floor): EnsureDefined<Floor> {
    return {
        __typename: "Floor",
        customData: floor.customData.map(mapToFlowConfigFieldValue),
        id: floor.id,
        displayId: floor.displayId === undefined || floor.displayId === null ? null : floor.displayId,
        level: floor.level,
        levelInfo: mapLevelInfo(floor.level),
        rooms: floor.rooms.map(mapToRoom),
        unrecognizedRooms: floor.unrecognizedRooms?.map(mapToRoom) ?? [], //TODO: der optionale fallback muss später weg
        shapeRepresentation: mapToShapeRepresentation(floor.shapeRepresentation),
        floorSlab: mapToConstructionPart(floor.floorSlab),
        ceilingSlab: floor.ceilingSlab === undefined || floor.ceilingSlab === null ? null : mapToConstructionPart(floor.ceilingSlab), //TODO: der optionale fallback muss später weg
        walls: floor.walls.map(mapToWall),
        furniture: floor.furniture?.map(mapToFurniture) ?? [], //TODO: der optionale fallback muss später weg
        pointsOfInterest: floor.pointsOfInterest?.map(mapToPointOfInterest) ?? [], //TODO: der optionale fallback muss später weg
        roofAreas: floor.roofAreas?.map(mapToRoofArea) ?? [], //TODO: der optionale fallback muss später weg
    }
}

function mapToRoofArea(roofArea: RoofArea): EnsureDefined<RoofArea> {
    return {
        __typename: "RoofArea",
        id: roofArea.id,
        displayId: roofArea.displayId === undefined || roofArea.displayId === null ? null : roofArea.displayId,
        customData: roofArea.customData.map(mapToFlowConfigFieldValue),
        shapeRepresentation: mapToShapeRepresentation(roofArea.shapeRepresentation),
        area: roofArea.area,
        perimeter: roofArea.perimeter,
        isFlat: roofArea.isFlat,
        isTriangular: roofArea.isTriangular,
        isRectangular: roofArea.isRectangular,
        roomId: roofArea.roomId,
        width: roofArea.width,
        height: roofArea.height,
    }
}

function mapLevelInfo(level: number): EnsureDefined<FloorLevelInfo> {
    if (level <= 0) {
        return {
            __typename: "FloorLevelInfo",
            levelType: "EG",
            number: 1
        };
    } else {
        return {
            __typename: "FloorLevelInfo",
            levelType: "OG",
            number: level
        };
    }
}

function mapToWall(wall: Wall): EnsureDefined<Wall> {
    return {
        __typename: "Wall",
        customData: wall.customData.map(mapToFlowConfigFieldValue),
        id: wall.id,
        displayId: wall.displayId === undefined || wall.displayId === null ? null : wall.displayId,
        openings: wall.openings.map(mapToConstructionPart),
        shapeRepresentation: mapToShapeRepresentation(wall.shapeRepresentation),
        roomIds: [...(wall.roomIds ?? [])], //TODO: <<<<<<<<<<<<<<<<< das muss noch eine weile so bleiben wegen der offine daten
        isExterior: wall.isExterior === true,
        isPartition: wall.isPartition === true,
        isIntermediate: wall.isIntermediate === true,
        sizeAdjustmentX: wall.sizeAdjustmentX,
        sizeAdjustmentY: wall.sizeAdjustmentY,
        sizeAdjustmentZ: wall.sizeAdjustmentZ,
        sizeAdjustmentUserX: wall.sizeAdjustmentUserX,
        sizeAdjustmentUserXRelatedWallIds: wall.sizeAdjustmentUserXRelatedWallIds,
        sizeAdjustmentUserY: wall.sizeAdjustmentUserY,
    }
}

function mapToRoom(room: Room): EnsureDefined<Room> {
    return {
        __typename: "Room",
        customData: room.customData.map(mapToFlowConfigFieldValue),
        furniture: room.furniture?.map(mapToFurniture) ?? [],  //TODO: <<<<<<<<<<<<<<<<< das muss noch eine weile so bleiben wegen der offine daten und der alten scans
        id: room.id,
        displayId: room.displayId === undefined || room.displayId === null ? null : room.displayId,
        roomNumber: room.roomNumber === undefined || room.roomNumber === null ? null : room.roomNumber,
        shapeRepresentation: mapToShapeRepresentation(room.shapeRepresentation),
        floorSlab: mapToConstructionPart(room.floorSlab),
        floorSlabSegments: room.floorSlabSegments?.map(mapToConstructionPart) ?? [], //TODO: <<<<<<<<<<<<<<<<< das muss noch eine weile so bleiben wegen der offine daten
        ceilingSlab: room.ceilingSlab === undefined || room.ceilingSlab === null ? null : mapToConstructionPart(room.ceilingSlab), //TODO: der optionale fallback muss später weg
        ceilingSlabSegments: room.ceilingSlabSegments?.map(mapToConstructionPart) ?? [], //TODO: <<<<<<<<<<<<<<<<< das muss noch eine weile so bleiben wegen der offine daten
        wallIds: [...(room.wallIds ?? [])], //TODO: <<<<<<<<<<<<<<<<< das muss noch eine weile so bleiben wegen der offine daten
    }
}

export function mapToFurniture(furniture: Furniture): EnsureDefined<Furniture> {
    return {
        __typename: "Furniture",
        customData: furniture.customData.map(mapToFlowConfigFieldValue),
        id: furniture.id,
        displayId: furniture.displayId === undefined || furniture.displayId === null ? null : furniture.displayId,
        shapeRepresentation: mapToShapeRepresentation(furniture.shapeRepresentation),
        type: furniture.type,
    }
}

export function mapToConstructionPart(constructionPart: ConstructionPart): EnsureDefined<ConstructionPart> {
    return {
        __typename: "ConstructionPart",
        customData: constructionPart.customData.map(mapToFlowConfigFieldValue),
        id: constructionPart.id,
        displayId: constructionPart.displayId === undefined || constructionPart.displayId === null ? null : constructionPart.displayId,
        shapeRepresentation: mapToShapeRepresentation(constructionPart.shapeRepresentation),
        type: constructionPart.type,
        foreignRoomId: constructionPart.foreignRoomId ?? null,
    }
}

export function mapToShapeRepresentation(shapeRepresentation: ShapeRepresentation): EnsureDefined<ShapeRepresentation> {
    const transformationMatrix = deepCopyMatrixArray(shapeRepresentation.transformationMatrix);

    switch (shapeRepresentation.shape.__typename) {
        case "Box":
            return {
                __typename: "ShapeRepresentation",
                transformationMatrix,
                shape: mapToShapeBox(shapeRepresentation.shape)
            }
        case "Polygon":
            return {
                __typename: "ShapeRepresentation",
                transformationMatrix,
                shape: mapToShapePolygon(shapeRepresentation.shape)
            }
        case "Ring":
            return {
                __typename: "ShapeRepresentation",
                transformationMatrix,
                shape: mapToShapeRing(shapeRepresentation.shape)
            }
        default:
            throw new Error("Unsupported shape representation input: " + shapeRepresentation.shape.__typename);
    }
}

function mapToShapeBox(box: Box): Shape & { __typename: "Box" } {
    return {
        __typename: "Box",
        width: box.width,
        height: box.height,
        depth: box.depth,
    }
}

function mapToShapePolygon(polygon: Polygon): EnsureDefined<Shape & { __typename: "Polygon" }> {
    return {
        __typename: "Polygon",
        extrusion: polygon.extrusion,
        extrusionDirection: mapToVector3D(polygon.extrusionDirection),
        vertices: polygon.vertices.map(mapToVector3D),
        holes: polygon.holes?.map(hole => hole.map(mapToVector3D)) ?? [], //TODO: später optional fallback entfernen
    }
}

function mapToShapeRing(ring: Ring): EnsureDefined<Shape & { __typename: "Ring" }> {
    return {
        __typename: "Ring",
        radius: ring.radius,
        size: ring.size,
        extrusion: ring.extrusion,
        extrusionDirection: mapToVector3D(ring.extrusionDirection),
        center: mapToVector2D(ring.center),
        startAngle: ring.startAngle,
        endAngle: ring.endAngle,
        thetaLength: ring.endAngle - ring.startAngle,
        innerRadius: ring.radius - ring.size / 2,
        outerRadius: ring.radius + ring.size / 2,
    }
}

function mapToVector3D(vector3D: Vector3D): EnsureDefined<Vector3D> {
    return {
        __typename: "Vector3D",
        x: vector3D.x,
        y: vector3D.y,
        z: vector3D.z,
    }
}

function mapToVector2D(vector2D: Vector2D): EnsureDefined<Vector2D> {
    return {
        __typename: "Vector2D",
        x: vector2D.x,
        y: vector2D.y,
    }
}

export function mapToFlowConfigFieldValue(flowConfigFieldValue: FlowConfigFieldValue): EnsureDefined<FlowConfigFieldValue> {
    return {
        __typename: "FlowConfigFieldValue",
        field: mapInputToFlowConfigField(flowConfigFieldValue.field),
        isComputed: flowConfigFieldValue.isComputed,
        arrayIndex: flowConfigFieldValue.arrayIndex === undefined ? null : flowConfigFieldValue.arrayIndex,
        valueBoolean: flowConfigFieldValue.valueBoolean === undefined ? null : flowConfigFieldValue.valueBoolean,
        valueDouble: flowConfigFieldValue.valueDouble === undefined ? null : flowConfigFieldValue.valueDouble,
        valueInteger: flowConfigFieldValue.valueInteger === undefined ? null : flowConfigFieldValue.valueInteger,
        valueString: flowConfigFieldValue.valueString === undefined ? null : flowConfigFieldValue.valueString,
    }
}

function mapInputToFlowConfigField(input: FlowConfigField): EnsureDefined<FlowConfigField> {
    return {
        __typename: "FlowConfigField",
        id: input.id,
    }
}