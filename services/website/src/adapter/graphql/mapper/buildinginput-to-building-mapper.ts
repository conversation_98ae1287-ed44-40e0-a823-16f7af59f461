import {BoxInput, Building, BuildingInput, BuildingPointOfInterest, BuildingPointOfInterestInput, ConstructionPart, ConstructionPartInput, Floor, FloorInput, FloorLevelInfo, FlowConfigFieldValue, Furniture, FurnitureInput, PolygonInput, RingInput, RoofArea, RoofAreaInput, Room, RoomInput, Shape, ShapeRepresentation, ShapeRepresentationInput, Vector2D, Vector2DInput, Vector3D, Vector3DInput, Wall, WallInput} from "@/adapter/graphql/generated/graphql";
import {deepCopyMatrixArray} from "@/components/listing/building/building";
import {EnsureDefined, mapInputToFlowConfigFieldValue} from "@/adapter/graphql/mapper/graphql-mapper";
import {createEmptyMutableFlowData} from "@/model/listing/FlowData";

export function buildingInputToBuilding(input: BuildingInput, isCustomDataComputed: boolean): EnsureDefined<Building> {
    const floorLevels = new Set<number>();
    for (const floor of input.floors) {
        floorLevels.add(floor.level);
    }

    return {
        __typename: "Building",
        customData: input.customData.map(cd => mapInputToFlowConfigFieldValue(cd, isCustomDataComputed)),
        floors: input.floors.map(f => mapInputToFloor(f, isCustomDataComputed)),
        id: input.id,
        displayId: input.displayId === undefined || input.displayId === null ? null : input.displayId,
        floorLevels: Array.from(floorLevels).sort(),
        shapeRepresentation: mapInputToShapeRepresentation(input.shapeRepresentation),
        pointsOfInterest: input.pointsOfInterest?.map(poi => mapInputToPointOfInterest(poi, isCustomDataComputed)) ?? [],
        rotationYCorrectionAngle: input.rotationYCorrectionAngle ?? 0,
    }
}

function mapInputToPointOfInterest(input: BuildingPointOfInterestInput, isCustomDataComputed: boolean): EnsureDefined<BuildingPointOfInterest> {
    return {
        __typename: "BuildingPointOfInterest",
        customData: input.customData.map(cd => mapInputToFlowConfigFieldValue(cd, isCustomDataComputed)),
        id: input.id,
        displayId: input.displayId === undefined || input.displayId === null ? null : input.displayId,
        transformationMatrix: deepCopyMatrixArray(input.transformationMatrix),
    }
}

export function mapInputToFloor(input: FloorInput, isCustomDataComputed: boolean): EnsureDefined<Floor> {
    return {
        __typename: "Floor",
        customData: input.customData.map(cd => mapInputToFlowConfigFieldValue(cd, isCustomDataComputed)),
        id: input.id,
        displayId: input.displayId === undefined || input.displayId === null ? null : input.displayId,
        level: input.level,
        levelInfo: mapLevelInfo(input.level),
        rooms: input.rooms.map(r => mapInputToRoom(r, isCustomDataComputed)),
        unrecognizedRooms: input.unrecognizedRooms?.map(r => mapInputToRoom(r, isCustomDataComputed)) ?? [], //TODO: der optionale fallback muss später weg
        shapeRepresentation: mapInputToShapeRepresentation(input.shapeRepresentation),
        floorSlab: mapInputToConstructionPart(input.floorSlab, isCustomDataComputed),
        ceilingSlab: input.ceilingSlab === undefined || input.ceilingSlab === null ? null : mapInputToConstructionPart(input.ceilingSlab, isCustomDataComputed),
        walls: input.walls.map(w => mapInputToWall(w, isCustomDataComputed)),
        furniture: input.furniture?.map(f => mapInputToFurniture(f, isCustomDataComputed)) ?? [], //TODO: der optionale fallback muss später weg
        pointsOfInterest: input.pointsOfInterest?.map(poi => mapInputToPointOfInterest(poi, isCustomDataComputed)) ?? [], //TODO: der optionale fallback muss später weg
        roofAreas: input.roofAreas?.map(ra => mapInputToRoofArea(ra, isCustomDataComputed)) ?? [], //TODO: der optionale fallback muss später weg
    }
}

function mapInputToRoofArea(input: RoofAreaInput, isCustomDataComputed: boolean): EnsureDefined<RoofArea> {
    return {
        __typename: "RoofArea",
        id: input.id,
        displayId: input.displayId === undefined || input.displayId === null ? null : input.displayId,
        customData: input.customData.map(cd => mapInputToFlowConfigFieldValue(cd, isCustomDataComputed)),
        shapeRepresentation: mapInputToShapeRepresentation(input.shapeRepresentation),
        area: input.area,
        perimeter: input.perimeter,
        isFlat: input.isFlat,
        isTriangular: input.isTriangular,
        isRectangular: input.isRectangular,
        roomId: input.roomId,
        width: input.width,
        height: input.height,
    }
}

function mapLevelInfo(level: number): EnsureDefined<FloorLevelInfo> {
    if (level <= 0) {
        return {
            __typename: "FloorLevelInfo",
            levelType: "EG",
            number: 1
        };
    } else {
        return {
            __typename: "FloorLevelInfo",
            levelType: "OG",
            number: level
        };
    }
}

function mapInputToWall(input: WallInput, isCustomDataComputed: boolean): EnsureDefined<Wall> {
    return {
        __typename: "Wall",
        customData: input.customData.map(cd => mapInputToFlowConfigFieldValue(cd, isCustomDataComputed)),
        id: input.id,
        displayId: input.displayId === undefined || input.displayId === null ? null : input.displayId,
        openings: input.openings.map(o => mapInputToConstructionPart(o, isCustomDataComputed)),
        shapeRepresentation: mapInputToShapeRepresentation(input.shapeRepresentation),
        roomIds: input.roomIds === null || input.roomIds === undefined ? [] : [...input.roomIds], //TODO: <<<<<<<<<<<<<<<<<< das kann bald vereinfacht werden, wenn das nicht mehr optional ist
        isExterior: input.isExterior === true,
        isPartition: input.isPartition === true,
        isIntermediate: input.isIntermediate === true,
        sizeAdjustmentX: input.sizeAdjustmentX === undefined || input.sizeAdjustmentX === null ? 0 : input.sizeAdjustmentX,
        sizeAdjustmentY: input.sizeAdjustmentY === undefined || input.sizeAdjustmentY === null ? 0 : input.sizeAdjustmentY,
        sizeAdjustmentZ: input.sizeAdjustmentZ === undefined || input.sizeAdjustmentZ === null ? 0 : input.sizeAdjustmentZ,
        sizeAdjustmentUserX: input.sizeAdjustmentUserX === undefined || input.sizeAdjustmentUserX === null ? 0 : input.sizeAdjustmentUserX,
        sizeAdjustmentUserXRelatedWallIds: input.sizeAdjustmentUserXRelatedWallIds === null || input.sizeAdjustmentUserXRelatedWallIds === undefined ? [] : [...input.sizeAdjustmentUserXRelatedWallIds],
        sizeAdjustmentUserY: input.sizeAdjustmentUserY === undefined || input.sizeAdjustmentUserY === null ? 0 : input.sizeAdjustmentUserY,
    }
}

function mapInputToRoom(input: RoomInput, isCustomDataComputed: boolean): EnsureDefined<Room> {
    return {
        __typename: "Room",
        customData: input.customData.map(cd => mapInputToFlowConfigFieldValue(cd, isCustomDataComputed)),
        furniture: input.furniture?.map(f => mapInputToFurniture(f, isCustomDataComputed)) ?? [], //TODO: <<<<<<<<<<<<<<<<< das muss noch eine weile so bleiben wegen der offine daten und der alten scans
        id: input.id,
        displayId: input.displayId === undefined || input.displayId === null ? null : input.displayId,
        roomNumber: input.roomNumber === undefined || input.roomNumber === null ? null : input.roomNumber,
        shapeRepresentation: mapInputToShapeRepresentation(input.shapeRepresentation),
        floorSlab: mapInputToConstructionPart(input.floorSlab, isCustomDataComputed),
        floorSlabSegments: input.floorSlabSegments?.map(f => mapInputToConstructionPart(f, isCustomDataComputed)) ?? [], //TODO: <<<<<<<<<<<<<<<<< das muss noch eine weile so bleiben wegen der offine daten
        ceilingSlab: input.ceilingSlab === undefined || input.ceilingSlab === null ? null : mapInputToConstructionPart(input.ceilingSlab, isCustomDataComputed), //TODO: der optionale fallback muss später weg
        ceilingSlabSegments: input.ceilingSlabSegments?.map(f => mapInputToConstructionPart(f, isCustomDataComputed)) ?? [], //TODO: <<<<<<<<<<<<<<<<< das muss noch eine weile so bleiben wegen der offine daten
        wallIds: input.wallIds === null || input.wallIds === undefined ? [] : [...input.wallIds], //TODO: <<<<<<<<<<<<<<<<<< das kann bald vereinfacht werden, wenn das nicht mehr optional ist
    }
}

function mapInputToFurniture(input: FurnitureInput, isCustomDataComputed: boolean): EnsureDefined<Furniture> {
    return {
        __typename: "Furniture",
        customData: input.customData.map(cd => mapInputToFlowConfigFieldValue(cd, isCustomDataComputed)),
        id: input.id,
        displayId: input.displayId === undefined || input.displayId === null ? null : input.displayId,
        shapeRepresentation: mapInputToShapeRepresentation(input.shapeRepresentation),
        type: input.type,
    }
}

function mapInputToConstructionPart(input: ConstructionPartInput, isCustomDataComputed: boolean): EnsureDefined<ConstructionPart> {
    return {
        __typename: "ConstructionPart",
        customData: input.customData.map(cd => mapInputToFlowConfigFieldValue(cd, isCustomDataComputed)),
        id: input.id,
        displayId: input.displayId === undefined || input.displayId === null ? null : input.displayId,
        shapeRepresentation: mapInputToShapeRepresentation(input.shapeRepresentation),
        type: input.type,
        foreignRoomId: input.foreignRoomId ?? null,
    }
}

export function mapInputToShapeRepresentation(input: ShapeRepresentationInput): EnsureDefined<ShapeRepresentation> {
    const transformationMatrix = deepCopyMatrixArray(input.transformationMatrix);

    if (input.box !== null && input.box !== undefined) {
        return {
            __typename: "ShapeRepresentation",
            transformationMatrix,
            shape: mapInputToShapeBox(input.box)
        };
    }
    if (input.polygon !== null && input.polygon !== undefined) {
        return {
            __typename: "ShapeRepresentation",
            transformationMatrix,
            shape: mapInputToShapePolygon(input.polygon)
        };
    }
    if (input.ring !== null && input.ring !== undefined) {
        return {
            __typename: "ShapeRepresentation",
            transformationMatrix,
            shape: mapInputToShapeRing(input.ring)
        };
    }
    throw new Error("Unsupported shape representation input");
}

function mapInputToShapeBox(input: BoxInput): Shape & { __typename: "Box" } {
    return {
        __typename: "Box",
        width: input.width,
        height: input.height,
        depth: input.depth,
    }
}

function mapInputToShapePolygon(input: PolygonInput): EnsureDefined<Shape & { __typename: "Polygon" }> {
    return {
        __typename: "Polygon",
        extrusion: input.extrusion,
        extrusionDirection: mapInputToVector3D(input.extrusionDirection),
        vertices: input.vertices.map(mapInputToVector3D),
        holes: input.holes?.map(hole => hole.vertices.map(mapInputToVector3D)) ?? [], //TODO: später optional fallback entfernen
    }
}

function mapInputToShapeRing(input: RingInput): EnsureDefined<Shape & { __typename: "Ring" }> {
    return {
        __typename: "Ring",
        radius: input.radius,
        size: input.size,
        extrusion: input.extrusion,
        extrusionDirection: mapInputToVector3D(input.extrusionDirection),
        center: mapInputToVector2D(input.center),
        startAngle: input.startAngle,
        endAngle: input.endAngle,
        thetaLength: input.endAngle - input.startAngle,
        innerRadius: input.radius - input.size / 2,
        outerRadius: input.radius + input.size / 2,
    }
}

function mapInputToVector3D(input: Vector3DInput): EnsureDefined<Vector3D> {
    return {
        __typename: "Vector3D",
        x: input.x,
        y: input.y,
        z: input.z,
    }
}

function mapInputToVector2D(input: Vector2DInput): EnsureDefined<Vector2D> {
    return {
        __typename: "Vector2D",
        x: input.x,
        y: input.y,
    }
}

export function mergeFieldValues(
    existingFieldValues: readonly FlowConfigFieldValue[],
    newFieldValues: readonly FlowConfigFieldValue[]
): EnsureDefined<FlowConfigFieldValue>[] {
    const flowData = createEmptyMutableFlowData()
    flowData.setFromInput(existingFieldValues)
    flowData.setFromInput(newFieldValues)
    return flowData.generateInput(false)
}